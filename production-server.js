const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const mysql = require('mysql2/promise');

const app = express();
const PORT = process.env.PORT || 3001;

// Database configuration
const dbConfig = {
  host: 'localhost',
  user: 'sonali_user',
  password: 'fe97f51e2647814ee59a3673dff44552ebddb38be6f03589b005eea7ebc6c6b8',
  database: 'sonali_app_production'
};

// JWT configuration
const JWT_SECRET = 'production-super-secret-jwt-key-change-this-in-real-production';
const JWT_EXPIRES_IN = '7d';

// Middleware
app.use(cors({
  origin: ['https://www.sonalibd.org', 'https://sonalibd.org'],
  credentials: true
}));
app.use(express.json());

// Database connection
let db;
async function connectDB() {
  try {
    db = await mysql.createConnection(dbConfig);
    console.log('✅ Database connected successfully');
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    process.exit(1);
  }
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    service: 'Sonali App Backend'
  });
});

// API status endpoint
app.get('/api/status', (req, res) => {
  res.json({ 
    status: 'API is running', 
    version: '1.0.0',
    timestamp: new Date().toISOString()
  });
});

// Test credentials endpoint
app.get('/api/auth/test-credentials', (req, res) => {
  res.json({
    message: 'Test credentials for Sonali App',
    credentials: [
      { role: 'admin', email: '<EMAIL>', password: 'admin123' },
      { role: 'manager', email: '<EMAIL>', password: 'manager123' },
      { role: 'field_officer', email: '<EMAIL>', password: 'officer123' }
    ]
  });
});

// Login endpoint
app.post('/api/auth/login', async (req, res) => {
  try {
    const { identifier, password, rememberMe } = req.body;

    if (!identifier || !password) {
      return res.status(400).json({
        success: false,
        message: 'Email/Member ID and password are required'
      });
    }

    // Find user by email or member ID
    const [users] = await db.execute(
      'SELECT * FROM users WHERE email = ? OR member_id = ? LIMIT 1',
      [identifier, identifier]
    );

    if (users.length === 0) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    const user = users[0];

    // Check if user is active
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'Account is deactivated'
      });
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Generate JWT token
    const tokenPayload = {
      userId: user.id,
      email: user.email,
      role: user.role,
      memberId: user.member_id
    };

    const accessToken = jwt.sign(tokenPayload, JWT_SECRET, {
      expiresIn: rememberMe ? '30d' : JWT_EXPIRES_IN
    });

    const refreshToken = jwt.sign(
      { userId: user.id, type: 'refresh' },
      JWT_SECRET,
      { expiresIn: '30d' }
    );

    // Update last login
    await db.execute(
      'UPDATE users SET lastLoginAt = NOW() WHERE id = ?',
      [user.id]
    );

    // Return success response
    res.json({
      success: true,
      message: 'Login successful',
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        memberId: user.member_id,
        isActive: user.isActive
      },
      accessToken,
      refreshToken,
      expiresAt: new Date(Date.now() + (rememberMe ? 30 * 24 * 60 * 60 * 1000 : 7 * 24 * 60 * 60 * 1000))
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Logout endpoint
app.post('/api/auth/logout', (req, res) => {
  res.json({
    success: true,
    message: 'Logout successful'
  });
});

// Protected route middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Access token required'
    });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({
        success: false,
        message: 'Invalid or expired token'
      });
    }
    req.user = user;
    next();
  });
};

// Protected dashboard endpoint
app.get('/api/dashboard', authenticateToken, (req, res) => {
  res.json({
    success: true,
    message: `Welcome to ${req.user.role} dashboard`,
    user: req.user
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    message: 'Internal server error'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint not found'
  });
});

// Start server
async function startServer() {
  await connectDB();
  
  app.listen(PORT, () => {
    console.log(`🚀 Sonali App Backend running on port ${PORT}`);
    console.log(`📊 Health check: http://localhost:${PORT}/health`);
    console.log(`🔗 API status: http://localhost:${PORT}/api/status`);
    console.log(`🔑 Test credentials: http://localhost:${PORT}/api/auth/test-credentials`);
  });
}

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('Received SIGINT, shutting down gracefully');
  if (db) {
    await db.end();
  }
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Received SIGTERM, shutting down gracefully');
  if (db) {
    await db.end();
  }
  process.exit(0);
});

startServer().catch(console.error);
